<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>千合商城 - 中医健康管理平台</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2B48C;
            --background-color: #FDF5E6;
            --text-color: #4A4A4A;
            --accent-color: #CD853F;
            --gold-color: #DAA520;
            --light-brown: #F5E6D3;
            --dark-brown: #654321;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background: linear-gradient(135deg, var(--background-color) 0%, #F8F0E3 100%);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
        }

        /* 顶部导航 */
        .header {
            background: var(--primary-color);
            color: #fff;
            padding: 15px;
            text-align: center;
            position: relative;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
            background-size: 20px 20px;
            border-bottom: 2px solid var(--secondary-color);
        }

        .header h1 {
            font-size: 24px;
            letter-spacing: 4px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        /* 底部导航 */
        .nav {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            padding: 8px 0 12px 0;
            border-top: 2px solid var(--secondary-color);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            text-align: center;
            color: var(--text-color);
            text-decoration: none;
            font-size: 11px;
            position: relative;
            padding: 5px 8px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 2px;
        }

        .nav-item div {
            font-size: 18px;
            margin-bottom: 2px;
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -12px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
        }

        /* 主要内容区域 */
        .main-content {
            padding: 0 20px 100px 20px;
        }

        /* 搜索框区域 */
        .search-section {
            padding: 20px 0;
            background: #fff;
        }

        .search-box {
            width: 100%;
            padding: 12px 16px 12px 45px;
            border: 2px solid var(--secondary-color);
            border-radius: 25px;
            font-size: 16px;
            background: #fff url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23D2B48C"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>') no-repeat 15px center;
            background-size: 20px 20px;
            transition: all 0.3s ease;
        }

        .search-box:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 10px rgba(139, 69, 19, 0.2);
        }

        /* 功能导航网格 */
        .function-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .function-card {
            background: linear-gradient(135deg, #fff 0%, var(--light-brown) 100%);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
            border: 1px solid var(--secondary-color);
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
            color: inherit;
        }

        .function-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.2);
        }

        .function-card .icon {
            width: 50px;
            height: 50px;
            margin: 0 auto 12px;
            background: var(--primary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }

        .function-card h3 {
            color: var(--primary-color);
            font-size: 16px;
            margin-bottom: 8px;
            font-weight: 700;
        }

        .function-card p {
            font-size: 12px;
            color: var(--text-color);
            opacity: 0.8;
            line-height: 1.4;
        }

        /* 快速预约卡片 */
        .quick-appointment {
            background: linear-gradient(135deg, var(--gold-color) 0%, #F4D03F 100%);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(218, 165, 32, 0.2);
            color: var(--dark-brown);
        }

        .quick-appointment h3 {
            font-size: 18px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quick-appointment .highlight {
            background: rgba(255, 255, 255, 0.3);
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 12px;
        }

        .appointment-input {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.8);
            margin: 10px 0;
            font-size: 14px;
        }

        .appointment-btn {
            background: var(--primary-color);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }

        .appointment-btn:hover {
            background: var(--dark-brown);
            transform: translateY(-2px);
        }

        /* 健康资讯卡片 */
        .health-news {
            background: #fff;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
            border-left: 4px solid var(--gold-color);
        }

        .health-news h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .news-item {
            display: flex;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .news-item:last-child {
            border-bottom: none;
        }

        .news-item:hover {
            background: var(--light-brown);
            border-radius: 8px;
            padding: 10px;
            margin: 0 -10px;
        }

        .news-item .news-icon {
            width: 40px;
            height: 40px;
            background: var(--secondary-color);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            font-size: 18px;
            color: var(--primary-color);
        }

        .news-item .news-content h4 {
            color: var(--primary-color);
            font-size: 14px;
            margin-bottom: 4px;
        }

        .news-item .news-content p {
            font-size: 12px;
            color: var(--text-color);
            opacity: 0.8;
        }

        /* 商城推荐区域 */
        .mall-section {
            background: #fff;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
        }

        .mall-section h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .product-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }

        .product-card {
            background: var(--light-brown);
            border-radius: 10px;
            padding: 12px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.15);
        }

        .product-card img {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            margin-bottom: 8px;
            object-fit: cover;
        }

        .product-card h4 {
            font-size: 14px;
            color: var(--primary-color);
            margin-bottom: 4px;
        }

        .product-card .price {
            font-size: 16px;
            color: var(--accent-color);
            font-weight: 700;
        }

        /* 医生推荐区域 */
        .doctor-section {
            background: #fff;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
        }

        .doctor-section h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .doctor-card {
            display: flex;
            align-items: center;
            padding: 15px;
            background: var(--light-brown);
            border-radius: 12px;
            margin-bottom: 12px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .doctor-card:last-child {
            margin-bottom: 0;
        }

        .doctor-card:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(139, 69, 19, 0.15);
        }

        .doctor-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            margin-right: 15px;
            border: 3px solid var(--secondary-color);
            object-fit: cover;
        }

        .doctor-info h4 {
            color: var(--primary-color);
            font-size: 16px;
            margin-bottom: 4px;
        }

        .doctor-info .specialty {
            font-size: 12px;
            color: var(--accent-color);
            margin-bottom: 4px;
        }

        .doctor-info .description {
            font-size: 12px;
            color: var(--text-color);
            opacity: 0.8;
        }

        /* 装饰元素 */
        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.03;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,10 Q60,30 50,50 Q40,30 50,10 M50,50 Q60,70 50,90 Q40,70 50,50" fill="%238B4513"/><circle cx="20" cy="20" r="3" fill="%23DAA520"/><circle cx="80" cy="80" r="3" fill="%23DAA520"/></svg>');
            background-size: 80px 80px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration"></div>
        
        <!-- 顶部导航 -->
        <header class="header">
            <h1>千合商城</h1>
        </header>

        <!-- 搜索框区域 -->
        <div class="search-section">
            <input type="search" class="search-box" placeholder="搜索医生、健康知识、中药材...">
        </div>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 功能导航 -->
            <div class="function-grid">
                <a href="appointment.html" class="function-card">
                    <div class="icon">📅</div>
                    <h3>预约咨询</h3>
                    <p>专业中医师在线咨询</p>
                </a>
                <a href="myhealth.html" class="function-card">
                    <div class="icon">📋</div>
                    <h3>健康档案</h3>
                    <p>个人健康数据管理</p>
                </a>
                <a href="health.html" class="function-card">
                    <div class="icon">📚</div>
                    <h3>健康话题</h3>
                    <p>中医养生知识分享</p>
                </a>
                <a href="catalog.html" class="function-card">
                    <div class="icon">🛒</div>
                    <h3>中药商城</h3>
                    <p>优质中药材选购</p>
                </a>
            </div>

            <!-- 快速预约 -->
            <div class="quick-appointment">
                <h3>🌟 快速预约 <span class="highlight">限时免费</span></h3>
                <p>输入推荐人验证码，享受专属优惠</p>
                <input type="text" class="appointment-input" placeholder="请输入推荐人验证码">
                <button class="appointment-btn">立即预约咨询</button>
            </div>

            <!-- 健康资讯 -->
            <div class="health-news">
                <h3>🍃 今日养生</h3>
                <div class="news-item">
                    <div class="news-icon">🌱</div>
                    <div class="news-content">
                        <h4>春季养肝正当时</h4>
                        <p>春养肝，夏养心，秋养肺，冬养肾，四季养脾胃</p>
                    </div>
                </div>
                <div class="news-item">
                    <div class="news-icon">🍵</div>
                    <div class="news-content">
                        <h4>药食同源话养生</h4>
                        <p>中医药膳调理，让食物成为最好的药物</p>
                    </div>
                </div>
                <div class="news-item">
                    <div class="news-icon">🧘</div>
                    <div class="news-content">
                        <h4>五脏六腑调理法</h4>
                        <p>传统中医理论指导现代健康生活</p>
                    </div>
                </div>
            </div>

            <!-- 商城推荐 -->
            <div class="mall-section">
                <h3>🏪 精选好物</h3>
                <div class="product-grid">
                    <div class="product-card">
                        <img src="https://via.placeholder.com/60x60/8B4513/FFFFFF?text=人参" alt="人参">
                        <h4>长白山人参</h4>
                        <div class="price">¥298</div>
                    </div>
                    <div class="product-card">
                        <img src="https://via.placeholder.com/60x60/CD853F/FFFFFF?text=枸杞" alt="枸杞">
                        <h4>宁夏枸杞</h4>
                        <div class="price">¥68</div>
                    </div>
                    <div class="product-card">
                        <img src="https://via.placeholder.com/60x60/DAA520/FFFFFF?text=燕窝" alt="燕窝">
                        <h4>印尼燕窝</h4>
                        <div class="price">¥1280</div>
                    </div>
                    <div class="product-card">
                        <img src="https://via.placeholder.com/60x60/D2B48C/FFFFFF?text=蜂蜜" alt="蜂蜜">
                        <h4>土蜂蜜</h4>
                        <div class="price">¥128</div>
                    </div>
                </div>
            </div>

            <!-- 医生推荐 -->
            <div class="doctor-section">
                <h3>👨‍⚕️ 名医推荐</h3>
                <div class="doctor-card">
                    <img src="https://via.placeholder.com/60x60/8B4513/FFFFFF?text=张" alt="张医生" class="doctor-avatar">
                    <div class="doctor-info">
                        <h4>张仲景</h4>
                        <div class="specialty">中医内科 · 主任医师</div>
                        <div class="description">擅长：脾胃调理、亚健康调理、慢性病管理</div>
                    </div>
                </div>
                <div class="doctor-card">
                    <img src="https://via.placeholder.com/60x60/CD853F/FFFFFF?text=李" alt="李医生" class="doctor-avatar">
                    <div class="doctor-info">
                        <h4>李时珍</h4>
                        <div class="specialty">中医妇科 · 副主任医师</div>
                        <div class="description">擅长：妇科调理、月经不调、更年期综合征</div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 底部导航 -->
        <nav class="nav">
            <a href="index.html" class="nav-item active">
                <div>🏠</div>
                <span>首页</span>
            </a>
            <a href="appointment.html" class="nav-item">
                <div>📅</div>
                <span>预约</span>
            </a>
            <a href="health.html" class="nav-item">
                <div>🍃</div>
                <span>健康</span>
            </a>
            <a href="catalog.html" class="nav-item">
                <div>🛒</div>
                <span>商城</span>
            </a>
            <a href="profile.html" class="nav-item">
                <div>👤</div>
                <span>我的</span>
            </a>
        </nav>
    </div>
</body>
</html> 