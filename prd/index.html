<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>好医生 - 中医健康管理平台</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #8B4513;
            --secondary-color: #D2B48C;
            --background-color: #FDF5E6;
            --text-color: #4A4A4A;
            --accent-color: #CD853F;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Serif SC', serif;
            background-color: var(--background-color);
            color: var(--text-color);
            line-height: 1.6;
        }

        .container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        /* 顶部导航 */
        .header {
            background: var(--primary-color);
            color: #fff;
            padding: 15px;
            text-align: center;
            position: relative;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M0,0 L100,0 L100,100 L0,100 Z" fill="none" stroke="%23D2B48C" stroke-width="2"/><path d="M20,20 L80,20 L80,80 L20,80 Z" fill="none" stroke="%23D2B48C" stroke-width="1"/><path d="M40,40 L60,40 L60,60 L40,60 Z" fill="none" stroke="%23D2B48C" stroke-width="0.5"/></svg>');
            background-size: 20px 20px;
            border-bottom: 2px solid var(--secondary-color);
        }

        .header h1 {
            font-size: 24px;
            letter-spacing: 4px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
        }

        /* 底部导航 */
        .nav {
            position: fixed;
            bottom: 0;
            width: 100%;
            max-width: 414px;
            background: #fff;
            display: flex;
            justify-content: space-around;
            padding: 10px;
            border-top: 2px solid var(--secondary-color);
            box-shadow: 0 -2px 10px rgba(0,0,0,0.1);
        }

        .nav-item {
            text-align: center;
            color: var(--text-color);
            text-decoration: none;
            font-size: 12px;
            position: relative;
            padding: 5px 0;
        }

        .nav-item.active {
            color: var(--primary-color);
        }

        .nav-item.active::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
        }

        /* 主要内容区域 */
        .main-content {
            padding: 20px;
            margin-bottom: 60px;
        }

        /* 预约卡片 */
        .appointment-card {
            background: #fff;
            border: 2px solid var(--secondary-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .appointment-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
        }

        /* 按钮样式 */
        .btn {
            background: var(--primary-color);
            color: #fff;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Noto Serif SC', serif;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .btn:hover::after {
            transform: translateX(100%);
        }

        /* 健康知识卡片 */
        .health-tips {
            background: #fff;
            border: 2px solid var(--secondary-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
        }

        .health-tips::after {
            content: '养生';
            position: absolute;
            top: -10px;
            right: 20px;
            background: var(--primary-color);
            color: #fff;
            padding: 2px 10px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* 医生信息卡片 */
        .doctor-card {
            display: flex;
            align-items: center;
            background: #fff;
            border: 2px solid var(--secondary-color);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            position: relative;
        }

        .doctor-avatar {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            margin-right: 15px;
            border: 3px solid var(--secondary-color);
            padding: 3px;
            background: #fff;
        }

        /* 搜索框 */
        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--secondary-color);
            border-radius: 4px;
            margin-bottom: 15px;
            font-family: 'Noto Serif SC', serif;
            background: #fff url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%238B4513"><path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/></svg>') no-repeat right 10px center;
            background-size: 20px;
            padding-right: 40px;
        }

        /* 装饰元素 */
        .decoration {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            opacity: 0.05;
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%238B4513"/><path d="M25,25 L75,25 L75,75 L25,75 Z" fill="%238B4513"/><path d="M37.5,37.5 L62.5,37.5 L62.5,62.5 L37.5,62.5 Z" fill="%238B4513"/></svg>');
            background-size: 50px 50px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="decoration"></div>
        
        <!-- 顶部导航 -->
        <header class="header">
            <h1>好医生</h1>
        </header>

        <!-- 主要内容区域 -->
        <main class="main-content">
            <!-- 搜索框 -->
            <input type="search" class="search-box" placeholder="搜索医生、健康知识...">

            <!-- 预约卡片 -->
            <div class="appointment-card">
                <h3>预约咨询</h3>
                <p>推荐人验证码：</p>
                <input type="text" class="search-box" placeholder="请输入推荐人验证码">
                <button class="btn">立即预约</button>
            </div>

            <!-- 健康知识 -->
            <div class="health-tips">
                <h3>今日养生</h3>
                <p>春养肝，夏养心，秋养肺，冬养肾...</p>
            </div>

            <!-- 医生推荐 -->
            <div class="doctor-card">
                <img src="https://via.placeholder.com/60" alt="医生头像" class="doctor-avatar">
                <div>
                    <h4>张医生</h4>
                    <p>中医内科 | 主任医师</p>
                    <p>擅长：脾胃调理、亚健康调理</p>
                </div>
            </div>
        </main>

        <!-- 底部导航 -->
        <nav class="nav">
            <a href="index.html" class="nav-item active">首页</a>
            <a href="appointment.html" class="nav-item">预约</a>
            <a href="health.html" class="nav-item">健康</a>
            <a href="community.html" class="nav-item">圈子</a>
            <a href="profile.html" class="nav-item">我的</a>
        </nav>
    </div>
</body>
</html> 