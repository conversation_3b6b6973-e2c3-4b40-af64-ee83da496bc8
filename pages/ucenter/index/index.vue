<template>
	<view class="container">
		<!-- 用户信息头部 -->
		<view class="profile-header">
			<button v-if="canIUseGetUserProfile" class="userinfo-btn" @tap="getUserProfile">
				<image class="profile-avatar" :src="userInfo.avatar" background-size="cover"></image>
				<view class="profile-info">
					<text class="profile-name">{{ userInfo.nickName || 'Hi,游客' }}</text>
					<text class="profile-bio">{{ userInfo.userName === '点击去登录' ? '点击登录获取更多服务' : '欢迎使用千合商城' }}</text>
				</view>
			</button>
			<button v-else class="userinfo-btn" open-type="getUserInfo" @getuserinfo="bindGetUserInfo">
				<image class="profile-avatar" :src="userInfo.avatar" background-size="cover"></image>
				<view class="profile-info">
					<text class="profile-name">{{ userInfo.nickName || 'Hi,游客' }}</text>
					<text class="profile-bio">{{ userInfo.userName === '点击去登录' ? '点击登录获取更多服务' : '欢迎使用千合商城' }}</text>
				</view>
			</button>
		</view>

		<!-- 健康数据统计 -->
		<view class="health-stats">
			<view class="stat-card">
				<view class="stat-value">{{ orderCount || 0 }}</view>
				<view class="stat-label">我的订单</view>
			</view>
			<view class="stat-card">
				<view class="stat-value">{{ couponCount || 0 }}</view>
				<view class="stat-label">优惠券</view>
			</view>
			<view class="stat-card">
				<view class="stat-value">{{ collectCount || 0 }}</view>
				<view class="stat-label">我的收藏</view>
			</view>
		</view>

		<!-- 功能菜单 -->
		<view class="menu-section">
			<view class="menu-item" @tap="navigateTo('/pages/ucenter/order/order')">
				<view class="menu-icon">📋</view>
				<view class="menu-text">我的订单</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @tap="navigateTo('/pages/ucenter/coupon/coupon')">
				<view class="menu-icon">🎫</view>
				<view class="menu-text">优惠券</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @tap="navigateTo('/pages/ucenter/collect/collect')">
				<view class="menu-icon">⭐</view>
				<view class="menu-text">我的收藏</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @tap="navigateTo('../address/address')">
				<view class="menu-icon">📍</view>
				<view class="menu-text">地址管理</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @tap="navigateTo('/pages/myappoint/myappoint')">
				<view class="menu-icon">📅</view>
				<view class="menu-text">我的预约</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @tap="navigateTo('/pages/myhealth/myhealth')">
				<view class="menu-icon">📋</view>
				<view class="menu-text">健康档案</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @tap="navigateTo('/pages/community/community')">
				<view class="menu-icon">👥</view>
				<view class="menu-text">我的社区</view>
				<view class="menu-arrow">></view>
			</view>
		</view>

		<!-- 设置菜单 -->
		<view class="menu-section">
			<view class="menu-item" @tap="navigateTo('/pages/ucenter/help/help')">
				<view class="menu-icon">❓</view>
				<view class="menu-text">帮助中心</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" @tap="navigateTo('/pages/ucenter/feedback/feedback')">
				<view class="menu-icon">💬</view>
				<view class="menu-text">意见反馈</view>
				<view class="menu-arrow">></view>
			</view>
			<view class="menu-item" v-if="!hasMobile" @tap="navigateTo('/pages/auth/mobile/mobile')">
				<view class="menu-icon">📱</view>
				<view class="menu-text">绑定用户</view>
				<view class="menu-arrow">></view>
			</view>
		</view>

		<!-- 客服联系 -->
		<button class="service-btn" open-type="contact">
			<view class="menu-item">
				<view class="menu-icon">🎧</view>
				<view class="menu-text">联系客服</view>
				<view class="menu-arrow">></view>
			</view>
		</button>

		<!-- 退出登录 -->
		<view class="logout-section" v-if="userInfo.userName!='点击去登录'">
			<button class="logout-btn" @tap="exitLogin">退出登录</button>
		</view>

		<!-- 公司信息 -->
		<view class="company-info">
			<text>深圳千合科技物流有限公司提供技术支持</text>
		</view>
	</view>
</template>

<script>
	const util = require("@/utils/util.js")
	const api = require('@/utils/api.js');
	const app = getApp();
	export default {
		data() {
			return {
				canIUseGetUserProfile: false,
				userInfo: {},
				hasMobile: '',
				orderCount: 0,
				couponCount: 0,
				collectCount: 0
			}
		},
		methods: {
			// 页面跳转方法
			navigateTo(url) {
				uni.navigateTo({
					url: url
				});
			},
			
			loginByWeixin: function(userInfo) {
				let code = null;
				return new Promise(function(resolve, reject) {
					return util.login().then((res) => {
						code = res.code;
						return userInfo;
					}).then((userInfo) => {
						//登录远程服务器
						util.request(api.AuthLoginByWeixin, {
							code: code,
							userInfo: userInfo
						}, 'POST', 'application/json').then(res => {
							if (res.errno === 0) {
								//存储用户信息
								uni.setStorageSync('userInfo', res.data.userInfo);
								uni.setStorageSync('token', res.data.token);

								resolve(res);
							} else {
								util.toast(res.errmsg)
								reject(res);
							}
						}).catch((err) => {
							reject(err);
						});
					}).catch((err) => {
						reject(err);
					})
				});
			},
			getUserProfile() {
				let that = this;
				// 推荐使用wx.getUserProfile获取用户信息，开发者每次通过该接口获取用户个人信息均需用户确认
				// 开发者妥善保管用户快速填写的头像昵称，避免重复弹窗
				wx.getUserProfile({
					desc: '用于完善会员资料', // 声明获取用户个人信息后的用途，后续会展示在弹窗中，请谨慎填写
					success: (resp) => {
						//登录远程服务器
						that.loginByWeixin(resp).then(res => {
							that.userInfo = res.data.userInfo
							app.globalData.userInfo = res.data.userInfo;
							app.globalData.token = res.data.token;
						}).catch((err) => {
							console.log(err)
						});
					}
				})
			},
			bindGetUserInfo(e) {
				let that = this;
				that.loginByWeixin(e.detail).then(res => {
					that.userInfo = res.data.userInfo
					app.globalData.userInfo = res.data.userInfo;
					app.globalData.token = res.data.token;
				}).catch((err) => {
					console.log(err)
				});
			},
			exitLogin: function() {
				uni.showModal({
					title: '',
					confirmColor: '#8B4513',
					content: '退出登录？',
					success: function(res) {
						if (res.confirm) {
							uni.removeStorageSync('token');
							uni.removeStorageSync('userInfo');
							app.globalData.userInfo = {
                nickname: 'Hi,游客',
								userName: '点击去登录',
								avatar: '@/static/images/noportait.png'
							}
							util.toast('退出成功');
							uni.switchTab({
								url: '/pages/index/index'
							});
						}
					}
				})
			},
			// 获取用户统计数据
			getUserStats() {
				// 这里可以调用API获取用户的订单数、优惠券数、收藏数等统计数据
				// 暂时使用模拟数据
				this.orderCount = 12;
				this.couponCount = 5;
				this.collectCount = 8;
			}
		},
		onShow: function() {
			let that = this;
			let userInfo = uni.getStorageSync('userInfo');
			let token = uni.getStorageSync('token');

			// 页面显示
			if (userInfo && token) {
				app.globalData.userInfo = userInfo;
				app.globalData.token = token;
			} else {
				uni.login({
					success: function(res) {
						if (res.code) {
							that.code = res.code
						}
					}
				});
			}
			that.userInfo = app.globalData.userInfo;
			
			// 获取用户统计数据
			that.getUserStats();
		},
		onLoad: function() {
			// 页面初始化 options为页面跳转所带来的参数
			if (wx.getUserProfile) {
				this.canIUseGetUserProfile = true
			}
		}
	}
</script>

<style lang="scss">
	page {
		height: 100%;
		width: 100%;
		background: #FDF5E6;
	}

	.container {
		background: #FDF5E6;
		min-height: 100vh;
		padding: 0;
		position: relative;
	}

	/* 用户信息头部 */
	.profile-header {
		background: linear-gradient(135deg, #8B4513 0%, #CD853F 100%);
		padding: 40rpx 30rpx;
		text-align: center;
		position: relative;
		overflow: hidden;
	}

	.profile-header::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><path d="M50,0 L100,50 L50,100 L0,50 Z" fill="%23ffffff" opacity="0.1"/></svg>');
		background-size: 100rpx 100rpx;
		pointer-events: none;
	}

	.userinfo-btn {
		background: transparent;
		border: none;
		padding: 0;
		margin: 0;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		z-index: 1;
	}

	.profile-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		border: 4rpx solid #D2B48C;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.2);
	}

	.profile-info {
		text-align: center;
	}

	.profile-name {
		display: block;
		font-size: 36rpx;
		color: #fff;
		font-weight: bold;
		margin-bottom: 8rpx;
	}

	.profile-bio {
		display: block;
		font-size: 24rpx;
		color: rgba(255,255,255,0.8);
	}

	/* 健康数据统计 */
	.health-stats {
		display: flex;
		padding: 30rpx;
		gap: 20rpx;
		margin-bottom: 20rpx;
	}

	.stat-card {
		flex: 1;
		background: #fff;
		border: 2rpx solid #D2B48C;
		border-radius: 16rpx;
		padding: 30rpx 20rpx;
		text-align: center;
		box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.1);
		transition: transform 0.2s ease;
	}

	.stat-card:active {
		transform: scale(0.95);
	}

	.stat-value {
		font-size: 48rpx;
		color: #8B4513;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.stat-label {
		font-size: 24rpx;
		color: #666;
	}

	/* 菜单区域 */
	.menu-section {
		background: #fff;
		border: 2rpx solid #D2B48C;
		border-radius: 16rpx;
		margin: 0 30rpx 20rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.1);
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #D2B48C;
		background: #fff;
		transition: background-color 0.2s ease;
	}

	.menu-item:last-child {
		border-bottom: none;
	}

	.menu-item:active {
		background-color: #FDF5E6;
	}

	.menu-icon {
		width: 48rpx;
		height: 48rpx;
		margin-right: 20rpx;
		font-size: 32rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.menu-text {
		flex: 1;
		font-size: 28rpx;
		color: #4A4A4A;
	}

	.menu-arrow {
		color: #999;
		font-size: 24rpx;
	}

	/* 客服按钮 */
	.service-btn {
		background: transparent;
		border: none;
		padding: 0;
		margin: 0 30rpx 20rpx;
		display: block;
	}

	/* 退出登录 */
	.logout-section {
		padding: 0 30rpx;
		margin-bottom: 20rpx;
	}

	.logout-btn {
		width: 100%;
		height: 88rpx;
		line-height: 88rpx;
		text-align: center;
		background: #fff;
		color: #8B4513;
		font-size: 28rpx;
		border: 2rpx solid #D2B48C;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 12rpx rgba(139, 69, 19, 0.1);
	}

	.logout-btn:active {
		background-color: #FDF5E6;
	}

	/* 公司信息 */
	.company-info {
		text-align: center;
		padding: 40rpx 30rpx;
		font-size: 20rpx;
		color: #999;
		line-height: 1.5;
	}
</style>
