<template>
    <view class="container">
        <!-- 顶部分类标签（带图标和展开按钮） -->
        <scroll-view scroll-x class="category-tabs-top">
            <view
                v-for="(tab, index) in tabs"
                :key="index"
                class="tab-item-top"
                :class="{ active: activeTab === index }"
                @tap="switchTab(index, tab.goodsClassCode)"
            >
                <image v-if="tab.icon" :src="tab.icon" class="tab-icon" mode="aspectFit" />
                <text>{{ tab.goodsClassName }}</text>
            </view>
            <view class="tab-expand-btn">
                <text>展开</text>
                <image src="/static/expand-arrow.png" class="expand-arrow" mode="aspectFit" />
            </view>
        </scroll-view>

        <!-- 主内容区域 -->
        <view class="main-content">
            <!-- 侧边栏（带标签和图标） -->
            <scroll-view scroll-y class="sidebar-new" :style="{height: windowHeight - 45 + 'px'}">
                <view
                    v-for="(category, index) in categoryList"
                    :key="index"
                    class="sidebar-item-new"
                    :class="{ active: currentCategory.itemLabelId === category.itemLabelId }"
                    @tap="switchCate(category.itemLabelId)"
                >
                    <image v-if="category.lablePic" :src="category.lablePic" class="sidebar-item-image-new" mode="aspectFit" />
                    <text>{{ category.labelTitle }}</text>
                    <view v-if="category.tag" class="sidebar-tag" :class="'tag-' + category.tagType">{{ category.tag }}</view>
                </view>
            </scroll-view>

            <!-- 商品网格 -->
            <scroll-view scroll-y class="product-grid-container-new" :style="{height: windowHeight - 45 + 'px'}">
                <view v-if="goodsList.length > 0" class="product-grid-new">
                    <view
                        v-for="(product, index) in goodsList"
                        :key="index"
                        class="product-card-new"
                        @tap="navigateToGoodsDetail(product.fbaInventoryId)"
                    >
                        <view class="product-img-wrap">
                            <image class="product-image-new" :src="product.smallImage" mode="aspectFill"></image>
                            <view v-if="product.tag" class="product-tag" :class="'tag-' + product.tagType">{{ product.tag }}</view>
                        </view>
                        <view class="info-new">
                            <text class="name-new">{{ product.productionName }}</text>
                            <view class="price-sales-new">
                                <text class="price-new">¥{{ product.listingPrice }}</text>
                                <text class="sales-new">售{{ product.sales || 0 }}</text>
                            </view>
                        </view>
                    </view>
                </view>
                <show-empty v-else text="该分类暂无商品"></show-empty>
            </scroll-view>
        </view>
    </view>
</template>

<script>
	const util = require("@/utils/util.js");
	const api = require('@/utils/api.js');	
import showEmpty from '@/components/show-empty/show-empty.vue';
import { mockRequest } from '@/utils/mockData.js';

export default {
    components: {
        showEmpty
    },
    data() {
        return {
            tabs: [],
            activeTab: 0,
            categoryList: [],
            currentCategory: {},
            goodsList: [],
            windowHeight: 0,
            loading: false
        };
    },
    onLoad: function() {
        // 获取设备窗口高度，用于设置滚动区域高度
        const systemInfo = uni.getSystemInfoSync();
        this.windowHeight = systemInfo.windowHeight;
        this.loadTopTabs();
    },
    methods: {
        // 加载顶部分类标签
		loadTopTabs() {
            if (this.loading) return;
            this.loading = true;

            uni.showLoading({
                title: '加载中...'
            });
			
            util.request(api.GoodsClassList).then(res => {
                this.tabs = res.data.data;
                if (this.tabs.length > 0) {
                    this.switchTab(0, this.tabs[0].goodsClassCode);
                }
            }).catch(err => {
                console.error('Failed to load top categories:', err);
                uni.showToast({
                    title: '加载分类失败',
                    icon: 'none'
                });
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
            });
        },
        
        // 切换顶部标签
        switchTab(index, tabId) {
            if (this.activeTab === index) return;
            this.activeTab = index;
            this.loadSidebarCategories(tabId);
        },
        
        // 加载侧边栏分类
		loadSidebarCategories(parentId) {
            if (this.loading) return;
            this.loading = true;

            uni.showLoading({
                title: '加载中...'
            });
			
            util.request(api.GoodsLabelList,{goodsClassCode:parentId}).then(res => {
                this.categoryList = res.data;
                if (this.categoryList.length > 0) {
                    this.switchCate(this.categoryList[0].itemLabelId);
                } else {
                    this.goodsList = [];
                    uni.hideLoading();
                    this.loading = false;
                }
            }).catch(err => {
                console.error('Failed to load sidebar categories:', err);
                uni.showToast({
                    title: '加载分类失败',
                    icon: 'none'
                });
                uni.hideLoading();
                this.loading = false;
            });
        },
        
        // 切换侧边栏分类
        switchCate(categoryId) {
            if (this.loading && this.currentCategory.id === categoryId) return;
            
            // 设置当前选中的分类
            this.currentCategory = this.categoryList.find(item => item.id === categoryId) || {};
            this.loading = true;
            
            uni.showLoading({
                title: '加载中...'
            });
            util.request(api.GoodsList,{categoryId:categoryId},'POST','application/json').then(res => {
                // 直接使用返回的商品数据属性名，修正属性映射
                this.goodsList = res.data.data
            }).catch(err => {
                console.error('Failed to load goods:', err);
                uni.showToast({
                    title: '加载商品失败',
                    icon: 'none'
                });
            }).finally(() => {
                uni.hideLoading();
                this.loading = false;
            });
        },
        
        // 跳转到商品详情页
        navigateToGoodsDetail(goodsId) {
            uni.navigateTo({
                url: `/pages/goods/goods?id=${goodsId}`
            });
        }
    }
};
</script>

<style lang="scss">
.container {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #f8f8f8;
}

/* 顶部分类标签 */
.category-tabs-top {
    display: flex;
    align-items: center;
    white-space: nowrap;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    height: 90rpx;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    padding: 0 10rpx;
}

.tab-item-top {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-width: 100rpx;
    margin-right: 20rpx;
    color: #333;
    font-size: 24rpx;
    cursor: pointer;
    transition: all 0.3s;
}

.tab-item-top.active {
    color: #e67e22;
    font-weight: 700;
}

.tab-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 4rpx;
}

.tab-expand-btn {
    display: flex;
    align-items: center;
    margin-left: 10rpx;
    color: #888;
    font-size: 22rpx;
    cursor: pointer;
}

.expand-arrow {
    width: 24rpx;
    height: 24rpx;
    margin-left: 4rpx;
}

/* 主内容区域 */
.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* 侧边栏 */
.sidebar-new {
    width: 120rpx;
    background-color: #fff;
    border-right: 1rpx solid #f0f0f0;
    height: calc(100vh - 90rpx);
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    padding-top: 10rpx;
}

.sidebar-item-new {
    width: 100%;
    padding: 18rpx 0 10rpx 0;
    text-align: center;
    font-size: 24rpx;
    color: #333;
    cursor: pointer;
    position: relative;
    background: #fff;
    border-left: 4rpx solid transparent;
    margin-bottom: 2rpx;
    transition: all 0.2s;
}

.sidebar-item-new.active {
    background: #f0f8ff;
    color: #e67e22;
    font-weight: 700;
    border-left: 4rpx solid #e67e22;
}

.sidebar-item-image-new {
    width: 36rpx;
    height: 36rpx;
    display: block;
    margin: 0 auto 4rpx auto;
}

.sidebar-tag {
    display: inline-block;
    margin-top: 4rpx;
    padding: 2rpx 8rpx;
    border-radius: 16rpx;
    font-size: 18rpx;
    color: #fff;
    background: #e67e22;
}

.tag-recommend { background: #4a90e2; }
.tag-hot { background: #e94f4f; }
.tag-new { background: #27ae60; }
.tag-flashsale { background: #e94f4f; }
.tag-seasonal { background: #27ae60; }

/* 商品网格 */
.product-grid-container-new {
    flex: 1;
    background-color: #fff;
    height: calc(100vh - 90rpx);
    padding: 0 20rpx;
}

.product-grid-new {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24rpx 16rpx;
}

.product-card-new {
    background: #fff;
    border-radius: 18rpx;
    overflow: hidden;
    box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
    transition: box-shadow 0.3s;
    margin-bottom: 8rpx;
    position: relative;
    display: flex;
    flex-direction: column;
    height: 320rpx;
}

.product-card-new:hover {
    box-shadow: 0 6rpx 18rpx rgba(0,0,0,0.12);
}

.product-img-wrap {
    position: relative;
    width: 100%;
    height: 160rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f8f8f8;
}

.product-image-new {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    object-fit: cover;
}

.product-tag {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
    padding: 2rpx 10rpx;
    border-radius: 12rpx;
    font-size: 18rpx;
    color: #fff;
    background: #e67e22;
    z-index: 2;
}

.tag-recommend { background: #4a90e2; }
.tag-hot { background: #e94f4f; }
.tag-new { background: #27ae60; }
.tag-flashsale { background: #e94f4f; }
.tag-seasonal { background: #27ae60; }

.info-new {
    flex: 1;
    padding: 12rpx 16rpx 8rpx 16rpx;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.name-new {
    font-size: 26rpx;
    font-weight: 600;
    color: #222;
    margin-bottom: 8rpx;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.price-sales-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-new {
    font-size: 26rpx;
    font-weight: 700;
    color: #e67e22;
}

.sales-new {
    font-size: 18rpx;
    color: #95a5a6;
}

.no-data {
    padding: 60rpx 0;
    text-align: center;
}
</style>

<style scoped>
/* General Styles */
.container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f9f9f9;
}

/* Category Tabs Styles */
.category-tabs-top {
    background-color: #ffffff;
    border-bottom: 1px solid #e8e8e8;
    white-space: nowrap;
    padding: 0 10px;
}

.tab-item-top {
    display: inline-block;
    padding: 12px 15px;
    font-size: 15px;
    color: #555;
    cursor: pointer;
    transition: color 0.3s ease, border-bottom-color 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab-item-top.active {
    color: #4a90e2; /* CornflowerBlue */
    font-weight: 600;
    border-bottom-color: #4a90e2;
}

.main-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar Styles */
.sidebar-new {
    width: 100px;
    background-color: #ffffff;
    border-right: 1px solid #e8e8e8;
}

.sidebar-item-new {
    padding: 18px 10px;
    text-align: center;
    font-size: 14px;
    color: #555;
    cursor: pointer;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.sidebar-item-new.active {
    background-color: #f0f8ff; /* AliceBlue */
    color: #2c3e50; /* Dark Slate Gray */
    font-weight: 600;
    border-left-color: #4a90e2; /* CornflowerBlue */
}

/* Product Grid Styles */
.product-grid-container-new {
    flex: 1;
    padding: 10px;
}

.product-grid-new {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.product-card-new {
    background-color: #ffffff;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.product-card-new:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

.product-image-new {
    width: 100%;
    height: 150px; /* Fixed height */
    display: block;
}

.info-new {
    padding: 12px;
}

.name-new {
    display: block;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 8px;
}

.price-sales-new {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.price-new {
    font-size: 16px;
    font-weight: bold;
    color: #e67e22; /* Carrot */
}

.sales-new {
    font-size: 12px;
    color: #95a5a6; /* Silver */
}
</style>
